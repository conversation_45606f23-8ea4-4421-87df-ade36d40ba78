import { describe, it, expect } from 'vitest'
import { schemaFieldDefinitions } from '../api/schemas'
import { schemaTypes } from '../api/mockData'

describe('Schema Updates', () => {
  it('should include all new schema types in schemaFieldDefinitions', () => {
    const expectedSchemas = [
      'ClaimReview',
      'Dataset', 
      'EmployerAggregateRating',
      'ItemList',
      'MathSolver',
      'Occupation',
      'Quiz'
    ]

    expectedSchemas.forEach(schemaType => {
      expect(schemaFieldDefinitions).toHaveProperty(schemaType)
      expect(schemaFieldDefinitions[schemaType]).toBeDefined()
      expect(Array.isArray(schemaFieldDefinitions[schemaType])).toBe(true)
    })
  })

  it('should have proper schema type categories', () => {
    const expectedCategories = [
      'E-commerce',
      'Organizations', 
      'Jobs',
      'Entertainment',
      'News',
      'Food and Drink',
      'Education and Science',
      'Basic',
      'Additional'
    ]

    const actualCategories = schemaTypes.map(category => category.label)
    
    expectedCategories.forEach(category => {
      expect(actualCategories).toContain(category)
    })
  })

  it('should have Jobs category with correct schemas', () => {
    const jobsCategory = schemaTypes.find(category => category.label === 'Jobs')
    expect(jobsCategory).toBeDefined()
    expect(jobsCategory?.options).toHaveProperty('EmployerAggregateRating')
    expect(jobsCategory?.options).toHaveProperty('Occupation')
    expect(jobsCategory?.options).toHaveProperty('JobPosting')
  })

  it('should have Entertainment category with correct schemas', () => {
    const entertainmentCategory = schemaTypes.find(category => category.label === 'Entertainment')
    expect(entertainmentCategory).toBeDefined()
    expect(entertainmentCategory?.options).toHaveProperty('Event')
    expect(entertainmentCategory?.options).toHaveProperty('ImageObject')
    expect(entertainmentCategory?.options).toHaveProperty('ItemList')
  })

  it('should have News category with correct schemas', () => {
    const newsCategory = schemaTypes.find(category => category.label === 'News')
    expect(newsCategory).toBeDefined()
    expect(newsCategory?.options).toHaveProperty('Article')
    expect(newsCategory?.options).toHaveProperty('ClaimReview')
    expect(newsCategory?.options).toHaveProperty('VideoObject')
  })

  it('should have Education and Science category with correct schemas', () => {
    const educationCategory = schemaTypes.find(category => category.label === 'Education and Science')
    expect(educationCategory).toBeDefined()
    expect(educationCategory?.options).toHaveProperty('Course')
    expect(educationCategory?.options).toHaveProperty('Dataset')
    expect(educationCategory?.options).toHaveProperty('MathSolver')
    expect(educationCategory?.options).toHaveProperty('Quiz')
    expect(educationCategory?.options).toHaveProperty('QAPage')
  })

  it('should have valid schema field structures', () => {
    // Test a few key schemas to ensure they have proper structure
    const claimReview = schemaFieldDefinitions.ClaimReview
    expect(claimReview).toBeDefined()
    expect(claimReview.length).toBeGreaterThan(0)
    
    // Should have Google Docs field
    const googleDocsField = claimReview.find(field => field.id === 'googleDocs')
    expect(googleDocsField).toBeDefined()
    expect(googleDocsField?.type).toBe('GoogleDocs')

    const dataset = schemaFieldDefinitions.Dataset
    expect(dataset).toBeDefined()
    expect(dataset.length).toBeGreaterThan(0)

    const mathSolver = schemaFieldDefinitions.MathSolver
    expect(mathSolver).toBeDefined()
    expect(mathSolver.length).toBeGreaterThan(0)
  })
})
